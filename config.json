{"window": {"title": "Interactive Markdown Writer", "width": 600, "height": 450, "min_width": 600, "min_height": 450}, "file": {"target": "interactive.md", "encoding": "utf-8"}, "presets": [{"name": "任务完成", "text": "✅ 任务已完成\n\n**完成时间**: {timestamp}\n**状态**: 已完成\n**详细说明**: \n\n", "category": "状态"}, {"name": "需要帮助", "text": "❓ 需要帮助\n\n**问题描述**: \n**期望结果**: \n**当前状态**: \n**紧急程度**: \n\n", "category": "求助"}, {"name": "进度更新", "text": "📊 进度更新\n\n**当前进度**: \n**完成内容**: \n**遇到的问题**: \n**下一步计划**: \n\n", "category": "进度"}, {"name": "问题反馈", "text": "🐛 问题反馈\n\n**问题类型**: \n**问题描述**: \n**重现步骤**: \n1. \n2. \n3. \n**期望行为**: \n**实际行为**: \n\n", "category": "问题"}, {"name": "想法记录", "text": "💡 想法记录\n\n**想法标题**: \n**想法内容**: \n**相关背景**: \n**可行性分析**: \n**下一步行动**: \n\n", "category": "创意"}, {"name": "会议记录", "text": "📝 会议记录\n\n**会议时间**: {timestamp}\n**会议主题**: \n**参与人员**: \n**讨论内容**: \n**决定事项**: \n**行动项**: \n- [ ] \n- [ ] \n- [ ] \n\n", "category": "会议"}, {"name": "学习笔记", "text": "📚 学习笔记\n\n**学习主题**: \n**学习时间**: {timestamp}\n**关键要点**: \n- \n- \n- \n**疑问**: \n**总结**: \n\n", "category": "学习"}, {"name": "代码审查", "text": "🔍 代码审查\n\n**审查时间**: {timestamp}\n**文件/模块**: \n**审查要点**: \n**发现问题**: \n**改进建议**: \n**评分**: ⭐⭐⭐⭐⭐\n\n", "category": "开发"}], "shortcuts": {"send": "Ctrl+Enter", "clear": "Ctrl+L", "preview": "F5", "quit": "Ctrl+Q"}, "ui": {"font_family": "Consolas", "font_size": 11, "input_width": 50, "input_height": 20, "button_width": 15}}