from mcp.server.fastmcp import FastMCP, Context
from mcp.server.elicitation import AcceptedElicitation, DeclinedElicitation, CancelledElicitation
from pydantic import BaseModel, Field
import os

mcp = FastMCP("InteractiveLoop")

# 文件路径
INTERACT_FILE = "interactive.md"

#检查文件是否存在，不存在就创建
if not os.path.exists(INTERACT_FILE):
    with open(INTERACT_FILE, 'w', encoding='utf-8') as f:
        f.write("")

def read_from_file():
    """从interactivate.md文件读取内容"""
    if os.path.exists(INTERACT_FILE):
        with open(INTERACT_FILE, 'r', encoding='utf-8') as f:
            return f.read()
    return "文件内容为空或文件不存在"


@mcp.tool(title="询问用户意见")
async def ask_user_for_comments(ctx: Context) -> str:
    """
    你需要在每一步工作完成后调用ask_user_for_comments获取用户反馈。
    """
    user_input = read_from_file()
    if user_input:
        with open(INTERACT_FILE, 'w', encoding='utf-8') as f:
            f.write("")
        return f"用户输入: {user_input}\n根据我的反馈，重新制定工作计划，但不要直接开始修改代码。而是要再次调用interactiveloop让我审核你的工作计划。"
    else:
        return "用户输入: 继续\n你要根据你的工作计划或推荐的方案，执行具体的工作任务或者修改代码，完成后调用interactiveloop等待我的审核与反馈。"


if __name__ == "__main__":
    mcp.run()
