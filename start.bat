@echo off
REM Windows批处理启动脚本
title Interactive Markdown Writer

echo.
echo ========================================
echo   Interactive Markdown Writer
echo ========================================
echo.
echo 正在启动工具...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

REM 运行程序
python run_writer.py

REM 如果程序异常退出，暂停以查看错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查错误信息
    pause
)