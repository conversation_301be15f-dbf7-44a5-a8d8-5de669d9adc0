#!/usr/bin/env python3
"""
Interactive Markdown Writer Tool
一个轻量级的工具，用于向interactive.md文件写入内容
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import os
import json
import threading
import time
from datetime import datetime
from typing import List, Dict, Optional


class InteractiveWriter:
    """交互式Markdown写入工具的主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.config = self.load_config()
        self.file_config = self.config.get("file", {})
        self.target_file = self.file_config.get("target", "interactive.md")
        self.encoding = self.file_config.get("encoding", "utf-8")
        
        # 文件同步相关
        self.last_file_mtime = 0
        self.last_content = ""
        self.is_updating_from_file = False
        self.is_updating_from_input = False
        self.file_monitor_thread = None
        self.stop_monitoring = False
        
        self.setup_window()
        self.setup_presets()
        self.create_widgets()
        self.setup_layout()
        # 在UI创建完成后再加载文件内容和启动监控
        self.load_file_content()
        self.start_file_monitoring()
        self.start_content_monitoring()
        print(f"✅ [初始化] Interactive Markdown Writer 已启动")
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print("⚠️  配置文件 config.json 未找到，使用默认配置")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"⚠️  配置文件格式错误: {e}，使用默认配置")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "window": {"title": "Interactive Markdown Writer", "width": 800, "height": 600},
            "file": {"target": "interactive.md", "encoding": "utf-8"},
            "presets": [],
            "ui": {"font_family": "Consolas", "font_size": 11, "input_width": 50, "input_height": 20}
        }
        
    def setup_window(self):
        """设置主窗口"""
        window_config = self.config.get("window", {})
        # 设置带有钓鱼女孩主题的标题
        default_title = "🎣 钓鱼女孩的笔记本 - Interactive Markdown Writer"
        self.root.title(window_config.get("title", default_title))
        
        width = window_config.get("width", 800)
        height = window_config.get("height", 600)
        self.root.geometry(f"{width}x{height}")
        
        min_width = window_config.get("min_width", 600)
        min_height = window_config.get("min_height", 400)
        self.root.minsize(min_width, min_height)
        
        # 设置窗口图标
        self.setup_icon()
    
    def setup_presets(self):
        """设置预设文本"""
        # 从配置文件加载预设，如果没有则使用默认预设
        config_presets = self.config.get("presets", [])
        if config_presets:
            self.presets = config_presets
        else:
            # 默认预设
            self.presets = [
                {
                    "name": "任务完成",
                    "text": "✅ 任务已完成\n\n**完成时间**: {timestamp}\n**状态**: 已完成\n**详细说明**: \n\n"
                },
                {
                    "name": "需要帮助",
                    "text": "❓ 需要帮助\n\n**问题描述**: \n**期望结果**: \n**当前状态**: \n\n"
                },
                {
                    "name": "进度更新",
                    "text": "📊 进度更新\n\n**当前进度**: \n**完成内容**: \n**下一步计划**: \n\n"
                },
                {
                    "name": "问题反馈",
                    "text": "🐛 问题反馈\n\n**问题类型**: \n**问题描述**: \n**重现步骤**: \n**期望行为**: \n\n"
                },
                {
                    "name": "想法记录",
                    "text": "💡 想法记录\n\n**想法内容**: \n**相关背景**: \n**可行性分析**: \n\n"
                },
                {
                    "name": "会议记录",
                    "text": "📝 会议记录\n\n**会议时间**: {timestamp}\n**参与人员**: \n**讨论内容**: \n**决定事项**: \n**行动项**: \n\n"
                }
            ]
    
    def create_widgets(self):
        """创建UI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=3)  # 左侧输入区域权重更大
        main_frame.columnconfigure(1, weight=1)  # 右侧按钮区域
        main_frame.rowconfigure(0, weight=1)
        
        # 左侧输入区域
        self.create_input_area(main_frame)
        
        # 右侧按钮区域
        self.create_button_area(main_frame)
    
    def create_input_area(self, parent):
        """创建左侧输入区域"""
        input_frame = ttk.LabelFrame(parent, text="内容输入", padding="5")
        input_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(0, weight=1)
        
        # 文本输入框（带滚动条）
        ui_config = self.config.get("ui", {})
        font_family = ui_config.get("font_family", "Consolas")
        font_size = ui_config.get("font_size", 11)
        input_width = ui_config.get("input_width", 50)
        input_height = ui_config.get("input_height", 20)
        
        self.text_input = scrolledtext.ScrolledText(
            input_frame,
            wrap=tk.WORD,
            width=input_width,
            height=input_height,
            font=(font_family, font_size)
        )
        self.text_input.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def create_button_area(self, parent):
        """创建右侧按钮区域"""
        button_frame = ttk.LabelFrame(parent, text="操作面板", padding="5")
        button_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        button_frame.columnconfigure(0, weight=1)
        
        # 预设按钮区域
        preset_label = ttk.Label(button_frame, text="预设模板:", font=("Arial", 10, "bold"))
        preset_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # 创建预设按钮
        for i, preset in enumerate(self.presets):
            btn = ttk.Button(
                button_frame,
                text=preset["name"],
                command=lambda p=preset: self.load_preset(p),
                width=15
            )
            btn.grid(row=i+1, column=0, sticky=(tk.W, tk.E), pady=2)
        
        # 分隔线
        separator = ttk.Separator(button_frame, orient='horizontal')
        separator.grid(row=len(self.presets)+1, column=0, sticky=(tk.W, tk.E), pady=10)
        
        # 操作按钮区域
        action_label = ttk.Label(button_frame, text="操作:", font=("Arial", 10, "bold"))
        action_label.grid(row=len(self.presets)+2, column=0, sticky=tk.W, pady=(0, 5))
        
        # 清空按钮
        clear_btn = ttk.Button(
            button_frame,
            text="清空输入框",
            command=self.clear_input,
            width=15
        )
        clear_btn.grid(row=len(self.presets)+3, column=0, sticky=(tk.W, tk.E), pady=2)
        
        # 状态标签
        self.status_label = ttk.Label(
            button_frame,
            text="就绪",
            foreground="green",
            font=("Arial", 9)
        )
        self.status_label.grid(row=len(self.presets)+4, column=0, sticky=tk.W, pady=(10, 0))
    
    def setup_layout(self):
        """设置布局"""
        # 绑定快捷键
        self.root.bind('<Control-l>', lambda e: self.clear_input())
        
        # 设置焦点
        self.text_input.focus_set()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def load_file_content(self):
        """加载文件内容到输入框"""
        try:
            if os.path.exists(self.target_file):
                with open(self.target_file, "r", encoding=self.encoding) as f:
                    content = f.read()
                
                self.is_updating_from_file = True
                self.text_input.delete("1.0", tk.END)
                self.text_input.insert("1.0", content)
                self.last_content = content
                self.last_file_mtime = os.path.getmtime(self.target_file)
                self.is_updating_from_file = False
                
                print(f"📂 [文件] 已加载 {len(content)} 字符")
                self.update_status("文件内容已加载", "green")
            else:
                # 创建空文件
                with open(self.target_file, "w", encoding=self.encoding) as f:
                    f.write("")
                
                # 确保输入框也是空的，并正确设置 last_content
                self.text_input.delete("1.0", tk.END)
                current_content = self.text_input.get("1.0", tk.END)
                if current_content.endswith('\n'):
                    current_content = current_content[:-1]
                self.last_content = current_content
                self.last_file_mtime = os.path.getmtime(self.target_file)
                
                print(f"📂 [文件] 已创建新文件: {self.target_file}")
                self.update_status("已创建新文件", "blue")
        except Exception as e:
            print(f"❌ [文件] 加载失败: {e}")
            self.update_status(f"加载文件失败: {e}", "red")
    
    def start_content_monitoring(self):
        """启动输入框内容监控"""
        self.monitor_content()
    
    def monitor_content(self):
        """监控输入框内容变化"""
        if not self.stop_monitoring:
            try:
                # 获取当前输入框内容
                current_content = self.text_input.get("1.0", tk.END)
                # 移除tkinter自动添加的最后一个换行符
                if current_content.endswith('\n'):
                    current_content = current_content[:-1]
                
                # 调试日志
                content_changed = current_content != self.last_content
                is_external_update = self.is_updating_from_file
                
                if content_changed:
                    print(f"🔍 [内容监控] 检测到内容变化 ({len(self.last_content)} → {len(current_content)} 字符)")
                
                # 检查内容是否发生变化，且不是由外部文件更新引起的
                if content_changed and not is_external_update:
                    # 延迟写入，避免频繁IO
                    if hasattr(self, '_write_timer'):
                        self.root.after_cancel(self._write_timer)
                    
                    # 立即更新 last_content 以避免重复检测
                    self.last_content = current_content
                    
                    # 创建一个闭包来正确捕获当前内容
                    def delayed_write():
                        self.write_to_file(current_content)
                    
                    self._write_timer = self.root.after(500, delayed_write)
                    print(f"⏰ [内容监控] 500ms后自动保存")
                
                # 每100ms检查一次内容变化
                self.root.after(100, self.monitor_content)
                
            except Exception as e:
                print(f"❌ [内容监控] 错误: {e}")
                import traceback
                traceback.print_exc()
                # 出错后继续监控
                self.root.after(1000, self.monitor_content)
    
    def write_to_file(self, content=None):
        """将输入框内容写入文件"""
        if self.is_updating_from_file:
            return
            
        try:
            # 如果没有传入内容，则从输入框获取
            if content is None:
                content = self.text_input.get("1.0", tk.END)
                # 移除tkinter自动添加的最后一个换行符
                if content.endswith('\n'):
                    content = content[:-1]
            
            # 写入文件
            print(f"💾 [写入文件] 保存 {len(content)} 字符到 {self.target_file}")
            self.is_updating_from_input = True
            
            with open(self.target_file, "w", encoding=self.encoding) as f:
                f.write(content)
            
            # 更新文件修改时间
            self.last_file_mtime = os.path.getmtime(self.target_file)
            self.is_updating_from_input = False
            
            print(f"✅ [写入文件] 保存成功")
            self.update_status("已自动保存", "green")
                
        except Exception as e:
            print(f"❌ [写入文件] 错误: {e}")
            self.update_status(f"保存失败: {e}", "red")
            self.is_updating_from_input = False
    
    def start_file_monitoring(self):
        """启动文件监控线程"""
        self.stop_monitoring = False
        self.file_monitor_thread = threading.Thread(target=self.monitor_file, daemon=True)
        self.file_monitor_thread.start()
    
    def monitor_file(self):
        """监控文件变化"""
        while not self.stop_monitoring:
            try:
                if os.path.exists(self.target_file):
                    current_mtime = os.path.getmtime(self.target_file)
                    if current_mtime != self.last_file_mtime and not self.is_updating_from_input:
                        # 文件被外部修改
                        with open(self.target_file, "r", encoding=self.encoding) as f:
                            new_content = f.read()
                        
                        if new_content != self.last_content:
                            # 在主线程中更新UI
                            self.root.after(0, self.update_from_file, new_content)
                            self.last_file_mtime = current_mtime
                
                time.sleep(0.5)  # 每500ms检查一次
            except Exception as e:
                print(f"文件监控错误: {e}")
                time.sleep(1)
    
    def update_from_file(self, content):
        """从文件更新输入框内容"""
        try:
            self.is_updating_from_file = True
            current_pos = self.text_input.index(tk.INSERT)
            self.text_input.delete("1.0", tk.END)
            self.text_input.insert("1.0", content)
            
            # 尝试恢复光标位置
            try:
                self.text_input.mark_set(tk.INSERT, current_pos)
            except:
                pass
            
            self.last_content = content
            self.is_updating_from_file = False
            self.update_status("文件已更新", "blue")
        except Exception as e:
            self.update_status(f"更新失败: {e}", "red")
    
    
    def on_closing(self):
        """窗口关闭时的处理"""
        self.stop_monitoring = True
        if self.file_monitor_thread and self.file_monitor_thread.is_alive():
            self.file_monitor_thread.join(timeout=1)
        self.root.destroy()
    
    def load_preset(self, preset: Dict[str, str]):
        """加载预设文本"""
        print(f"🔘 [预设] 插入模板: {preset['name']}")
        
        # 获取当前光标位置
        current_pos = self.text_input.index(tk.INSERT)
        
        # 插入预设文本，替换时间戳
        text = preset["text"].format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        # 在当前位置插入预设文本
        self.text_input.insert(current_pos, text)
        
        # 设置焦点
        self.text_input.focus_set()
        
        # 更新状态
        self.update_status(f"已插入预设: {preset['name']}", "blue")
    
    def clear_input(self):
        """清空输入框"""
        self.text_input.delete("1.0", tk.END)
        self.text_input.focus_set()
        self.update_status("输入框已清空", "orange")
    
    
    def update_status(self, message: str, color: str = "black"):
        """更新状态标签"""
        self.status_label.config(text=message, foreground=color)
        # 3秒后恢复默认状态
        self.root.after(3000, lambda: self.status_label.config(text="就绪", foreground="green"))
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = InteractiveWriter()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()