# Interactive Markdown Writer 使用说明

## 📖 简介

这是一个轻量级的Python GUI工具，用于向Markdown文件写入内容。特别适合记录想法、进度更新、会议记录等。

## 🚀 快速开始

### 启动方式

```bash
# 方式1：直接运行主程序
python interactive_writer.py

# 方式2：使用启动脚本
python run_writer.py
```

### 系统要求

- Python 3.6+
- tkinter（通常随Python一起安装）
- 无需额外依赖

## 🎯 功能特性

### 主要功能

1. **文本输入区域**（左侧）
   - 支持多行文本输入
   - 自动换行
   - 滚动条支持

2. **预设模板按钮**（右侧上方）
   - 任务完成
   - 需要帮助
   - 进度更新
   - 问题反馈
   - 想法记录
   - 会议记录
   - 学习笔记
   - 代码审查

3. **操作按钮**（右侧下方）
   - 清空输入框

### 快捷键

- `Ctrl + L`：清空输入框

## ⚙️ 配置说明

工具支持通过 `config.json` 文件进行配置：

### 窗口配置
```json
"window": {
  "title": "Interactive Markdown Writer",
  "width": 800,
  "height": 600,
  "min_width": 600,
  "min_height": 400
}
```

### 文件配置
```json
"file": {
  "target": "interactive.md",
  "encoding": "utf-8"
}
```

### UI配置
```json
"ui": {
  "font_family": "Consolas",
  "font_size": 11,
  "input_width": 50,
  "input_height": 20,
  "button_width": 15
}
```

### 自定义预设模板

可以在 `config.json` 中添加自定义预设：

```json
"presets": [
  {
    "name": "自定义模板",
    "text": "## 自定义内容\n\n**时间**: {timestamp}\n**内容**: \n\n",
    "category": "自定义"
  }
]
```

## 📝 使用流程

1. **启动程序**
   ```bash
   python run_writer.py
   ```

2. **实时同步**
   - 程序启动后自动开启实时同步
   - 输入框内容变化时自动保存到文件（500ms延迟）
   - 外部修改文件时自动更新到输入框

3. **输入内容**
   - 直接在左侧输入框输入内容
   - 或点击右侧预设按钮在光标位置插入模板
   - 支持Markdown格式
   - 所有内容变化都会自动保存

## 🎨 界面说明

```
┌─────────────────────────────────────────────────────────────┐
│                Interactive Markdown Writer                  │
├─────────────────────────────────┬───────────────────────────┤
│           内容输入              │        操作面板           │
│  ┌─────────────────────────────┐│  ┌─────────────────────┐  │
│  │                             ││  │    预设模板:        │  │
│  │                             ││  │  ┌───────────────┐  │  │
│  │      文本输入区域           ││  │  │   任务完成    │  │  │
│  │                             ││  │  └───────────────┘  │  │
│  │                             ││  │  ┌───────────────┐  │  │
│  │                             ││  │  │   需要帮助    │  │  │
│  │                             ││  │  └───────────────┘  │  │
│  │                             ││  │        ...          │  │
│  │                             ││  │                     │  │
│  │                             ││  │    操作:            │  │
│  │                             ││  │  ┌───────────────┐  │  │
│  │                             ││  │  │   清空输入框  │  │  │
│  │                             ││  │  └───────────────┘  │  │
│  │                             ││  │                     │  │
│  │                             ││  │                     │  │
│  │                             ││  │                     │  │
│  │                             ││  │                     │  │
│  └─────────────────────────────┘│  │                     │  │
│                                 │  │                     │  │
│                                 │  │  状态: 已自动保存   │  │
└─────────────────────────────────┴───────────────────────────┘
```

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本（需要3.6+）
   - 确保tkinter已安装：`python -c "import tkinter"`

2. **配置文件错误**
   - 检查 `config.json` 格式是否正确
   - 删除配置文件使用默认配置

3. **文件写入失败**
   - 检查目标文件是否有写入权限
   - 确保目标目录存在

4. **中文显示问题**
   - 确保系统支持UTF-8编码
   - 检查字体设置

### 日志信息

程序启动时会在控制台显示状态信息：
- ✅ 正常启动
- ⚠️ 警告信息
- ❌ 错误信息

## 📂 文件结构

```
project/
├── interactive_writer.py    # 主程序文件
├── run_writer.py           # 启动脚本
├── config.json             # 配置文件
├── 使用说明.md             # 本文件
├── interactive.md          # 输出文件（自动创建）
└── README.md              # 项目说明
```

## 🎯 最佳实践

1. **模板使用**
   - 为常用场景创建专门的模板
   - 使用 `{timestamp}` 占位符自动插入时间

2. **内容组织**
   - 使用Markdown格式提高可读性
   - 适当使用标题、列表、强调等格式

3. **文件管理**
   - 定期备份 `interactive.md` 文件
   - 考虑按日期或项目分类管理

4. **快捷键使用**
   - 熟练使用快捷键提高效率
   - `Ctrl + Enter` 是最常用的快捷键

## 🔄 更新日志

### v1.0.0
- 基础功能实现
- 支持预设模板
- 配置文件支持
- 快捷键支持

---

💡 **提示**: 如果你有任何问题或建议，欢迎反馈！