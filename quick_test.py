#!/usr/bin/env python3
"""
快速测试脚本 - 验证日志输出和基本功能
"""

import os
import time

def test_basic_functionality():
    """基本功能测试"""
    print("🧪 快速功能测试")
    print("=" * 40)
    
    # 清理测试环境
    test_file = "interactive.md"
    if os.path.exists(test_file):
        print(f"🧹 清理现有文件: {test_file}")
        os.remove(test_file)
    
    print("✅ 测试环境准备完成")
    print("\n💡 现在请启动工具:")
    print("   python interactive_writer.py")
    print("\n📋 观察启动日志，应该看到:")
    print("   🚀 [初始化] 开始加载文件内容和启动监控")
    print("   📂 [加载文件] 开始加载 interactive.md")
    print("   📄 [加载文件] 文件不存在，创建新文件")
    print("   🔍 [内容监控] 启动内容监控")
    print("   ✅ [初始化] 初始化完成")
    print("\n🔘 然后点击任意预设按钮，应该看到:")
    print("   🔘 [预设按钮] 点击预设: xxx")
    print("   🔍 [内容监控] 检测到内容变化")
    print("   📝 [写入文件] 开始执行")
    print("   💾 [写入文件] 开始写入到 interactive.md")
    print("   ✅ [写入文件] 写入成功")
    
    print("\n⌨️  或者直接在输入框中输入内容，应该看到类似的日志")
    print("\n🔍 如果没有看到预期的日志，说明存在问题需要进一步调试")

if __name__ == "__main__":
    test_basic_functionality()