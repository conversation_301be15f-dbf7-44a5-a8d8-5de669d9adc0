#!/bin/bash
# Linux/Mac shell启动脚本

echo ""
echo "========================================"
echo "   Interactive Markdown Writer"
echo "========================================"
echo ""
echo "正在启动工具..."
echo ""

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python，请确保Python已安装"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 检查文件是否存在
if [ ! -f "run_writer.py" ]; then
    echo "❌ 错误: 未找到 run_writer.py 文件"
    exit 1
fi

# 运行程序
echo "🚀 启动程序..."
$PYTHON_CMD run_writer.py

# 检查退出状态
if [ $? -ne 0 ]; then
    echo ""
    echo "⚠️  程序异常退出"
    read -p "按Enter键继续..."
fi