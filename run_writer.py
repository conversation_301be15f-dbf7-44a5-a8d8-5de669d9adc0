#!/usr/bin/env python3
"""
启动脚本 - Interactive Markdown Writer
简化的启动入口
"""

import sys
import os

# 确保当前目录在Python路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from interactive_writer import main
    
    if __name__ == "__main__":
        print("🚀 启动 Interactive Markdown Writer...")
        print("📝 这个工具将帮助你向 interactive.md 文件写入内容")
        print("💡 使用预设模板或自定义内容，支持实时同步")
        print("⌨️  快捷键: Ctrl+L清空, F5预览, Ctrl+S切换同步")
        print("-" * 50)
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保 interactive_writer.py 文件存在")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)